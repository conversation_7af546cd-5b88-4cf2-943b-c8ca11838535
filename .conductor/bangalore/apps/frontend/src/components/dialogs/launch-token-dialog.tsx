'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import {
  useCurrentAccount,
  useSignAndExecuteTransaction,
} from '@mysten/dapp-kit';
import { FeatherIcon, GlobeIcon, Loader2 } from 'lucide-react';
import { useTranslations } from 'next-intl';
import type { ComponentProps } from 'react';
import { useEffect, useMemo, useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import z from 'zod';

import { TelegramIcon, XIcon } from '@/assets/icons';
import { FormattedNumberInput } from '@/components/format-number-input';
import { ImageUpload } from '@/components/image-upload';
import { ZKLoginErrorHandler } from '@/components/zklogin-error-handler';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { ToggleGroup, ToggleGroupItem } from '@/components/ui/toggle-group';
import { WalletConnectButton } from '@/components/wallet-connect-button';
import { networkConfigService } from '@/services/network-config.service';
import {
  TokenCreationError,
  TokenCreationService,
} from '@/services/token-creation.service';
import { cn } from '@/utils/classnames';

interface LaunchTokenDialogProps extends ComponentProps<typeof Dialog> {
  onTokenCreated?: (result: {
    packageId: string;
    bondingCurveId?: string;
    tokenData?: {
      name: string;
      symbol: string;
      description?: string;
      decimals: number;
      totalSupply: number;
      imageUrl?: string;
    };
  }) => void;
}

const PLATFORM_OPTIONS = ['hop.fun'] as const;

export const LaunchTokenDialog = ({
  children,
  ...props
}: LaunchTokenDialogProps) => {
  const [showOptions, setShowOptions] = useState<string | undefined>(undefined);

  const t = useTranslations('dialogs.launch-token');

  const DESCRIPTION_PLACEHOLDERS = Array.from({ length: 12 }, (_, i) =>
    t(`form.description.placeholders.${i + 1}`),
  );

  const TOKEN_SUPPLY_OPTIONS = [
    { label: '69M', value: 69_000_000 },
    { label: '420M', value: 420_000_000 },
    { label: '1B', value: 1_000_000_000 },
    { label: '69B', value: 69_000_000_000 },
    { label: '420B', value: 420_000_000_000 },
    { label: '1T', value: 1_000_000_000_000 },
    { label: 'Custom', value: 'custom' },
  ] as const;

  const createTokenSchema = z
    .object({
      imageUrl: z.url(t('form.image.validation.url')).optional(),
      name: z
        .string()
        .min(1, t('form.name.validation.required'))
        .max(32, t('form.name.validation.max')),
      symbol: z
        .string()
        .min(1, t('form.symbol.validation.required'))
        .max(10, t('form.symbol.validation.max')),
      description: z.string().optional(),
      website: z.url(t('form.website.validation.url')).optional(),
      twitter: z.url(t('form.twitter.validation.url')).optional(),
      telegram: z.url(t('form.telegram.validation.url')).optional(),
      platform: z
        .enum(PLATFORM_OPTIONS)
        .optional()
        .default(PLATFORM_OPTIONS[0]),
      decimals: z
        .number()
        .min(0, t('form.decimals.validation.min'))
        .max(18, t('form.decimals.validation.max'))
        .default(6),
      tokenSupply: z
        .enum(TOKEN_SUPPLY_OPTIONS.map((opt) => opt.label))
        .default('69M')
        .optional(),
      customTokenSupply: z
        .number()
        .min(10000000, t('form.custom-token-supply.validation.min'))
        .positive(t('form.custom-token-supply.validation.positive'))
        .optional(),
    })
    .refine(
      (data) => {
        // Ensure that if 'Custom' is selected, customTokenSupply is provided and valid
        if (data.tokenSupply === 'Custom') {
          return (
            data.customTokenSupply !== undefined &&
            data.customTokenSupply >= 10000000
          );
        }
        return true;
      },
      {
        message: t('form.custom-token-supply.validation.required'),
        path: ['customTokenSupply'],
      },
    );

  const form = useForm({
    resolver: zodResolver(createTokenSchema),
    defaultValues: {
      name: '',
      symbol: '',
      description: '',
      platform: PLATFORM_OPTIONS[0],
      website: '',
      twitter: '',
      telegram: '',
      decimals: 6,
      tokenSupply: '69M',
      customTokenSupply: undefined,
    },
  });

  useEffect(() => {
    // Reset options when the dialog is closed
    if (!props.open) {
      setShowOptions(undefined);
      form.reset({
        name: '',
        symbol: '',
        description: '',
        platform: PLATFORM_OPTIONS[0],
        decimals: 6,
        tokenSupply: '69M',
        customTokenSupply: undefined,
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [props.open]);

  const randomPlaceholder = useMemo(() => {
    const placeholders = DESCRIPTION_PLACEHOLDERS;
    return placeholders[Math.floor(Math.random() * placeholders.length)];
  }, [DESCRIPTION_PLACEHOLDERS]);

  const [isCreating, setIsCreating] = useState(false);
  const [creationStep, setCreationStep] = useState<
    'idle' | 'publishing' | 'bonding' | 'complete'
  >('idle');
  const [zkLoginError, setZkLoginError] = useState<Error | null>(null);
  const currentAccount = useCurrentAccount();
  const { mutateAsync: signAndExecuteTransaction } =
    useSignAndExecuteTransaction();

  const onSubmit = async (data: z.infer<typeof createTokenSchema>) => {
    console.log('Creating token with data:', data);

    try {
      // Reset any previous ZKLogin errors
      setZkLoginError(null);

      // Check if wallet is connected
      if (!currentAccount) {
        toast.error('Please connect your wallet first');
        return;
      }

      // Check if network is supported
      if (!TokenCreationService.isNetworkSupported()) {
        const network = networkConfigService.getCurrentNetwork();
        toast.error(
          `Token creation is not available on ${network}. Please switch to devnet or mainnet.`,
        );
        return;
      }

      setIsCreating(true);
      setCreationStep('publishing');

      // Determine the actual token supply based on the selection
      let actualSupply: number;
      if (data.tokenSupply === 'Custom') {
        if (!data.customTokenSupply) {
          toast.error('Please enter a custom token supply');
          return;
        }
        actualSupply = data.customTokenSupply;
      } else {
        const supplyOption = TOKEN_SUPPLY_OPTIONS.find(
          (opt) => opt.label === data.tokenSupply,
        );
        if (!supplyOption || supplyOption.value === 'custom') {
          toast.error('Invalid token supply selection');
          return;
        }
        actualSupply = supplyOption.value;
      }

      // Prepare the payload for token creation
      const payload = {
        name: data.name,
        symbol: data.symbol,
        description: data.description,
        imageUrl: data.imageUrl,
        twitter: data.twitter,
        website: data.website,
        telegram: data.telegram,
        totalSupply: actualSupply,
        decimals: data.decimals,
      };

      // Validate the payload
      const validation = TokenCreationService.validatePayload(payload);
      if (!validation.valid) {
        toast.error(validation.errors[0] || 'Invalid token data');
        return;
      }

      toast.info('Publishing token contract to the blockchain...');

      // Execute the token creation
      setCreationStep('bonding');
      const result = await TokenCreationService.executeTokenCreation(
        payload,
        currentAccount.address,
        signAndExecuteTransaction,
      );

      setCreationStep('complete');

      // Success!
      toast.success(
        `Token created successfully! Package ID: ${result.packageId}`,
      );

      if (result.bondingCurveId) {
        toast.success(`Bonding curve created: ${result.bondingCurveId}`);
      }

      // Call the onTokenCreated callback if provided
      props.onTokenCreated?.({
        ...result,
        tokenData: {
          name: data.name,
          symbol: data.symbol,
          description: data.description,
          decimals: data.decimals,
          totalSupply: actualSupply,
          imageUrl: data.imageUrl,
        },
      });

      // Reset form and close dialog
      form.reset();
      setZkLoginError(null);
      props.onOpenChange?.(false);
    } catch (error) {
      console.error('Token creation error:', error);

      // Check if this is a ZKLogin error
      const errorObj = error as any;
      const errorMessage = errorObj?.message || errorObj?.details?.message || '';
      const isZKLoginError = 
        errorMessage.includes('ZKLogin max epoch too large') ||
        errorMessage.includes('Invalid user signature') ||
        errorMessage.includes('Signature is not valid');

      if (isZKLoginError) {
        // Set the ZKLogin error for display
        setZkLoginError(error as Error);
        // Don't show toast for ZKLogin errors as the error handler will display them
      } else {
        // Handle other error types
        const friendlyMessage = TokenCreationService.getErrorMessage(error);
        toast.error(friendlyMessage);
      }

      // Log detailed error for debugging
      if (error instanceof TokenCreationError) {
        console.error('Token creation failed:', {
          code: error.code,
          details: error.details,
        });
      }
    } finally {
      setIsCreating(false);
      setCreationStep('idle');
    }
  };

  return (
    <Dialog {...props}>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>{t('title')}</DialogTitle>
          <DialogDescription>{t('description')}</DialogDescription>
        </DialogHeader>
        {zkLoginError ? (
          <ZKLoginErrorHandler 
            error={zkLoginError} 
            onRetry={() => {
              setZkLoginError(null);
              form.handleSubmit(onSubmit)();
            }}
          />
        ) : (
        <Form {...form}>
          <form
            className="flex flex-col gap-4"
            onSubmit={form.handleSubmit(onSubmit)}
          >
            <ScrollArea
              className="max-h-[calc(100vh-200px)] xl:h-fit"
              type="scroll"
            >
              <div className="space-y-4 px-1">
                <FormField
                  control={form.control}
                  name="imageUrl"
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                        <ImageUpload onUploadComplete={field.onChange} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('form.name.label')}</FormLabel>
                      <FormControl>
                        <Input
                          placeholder={t('form.name.placeholder')}
                          type="text"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="symbol"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('form.symbol.label')}</FormLabel>
                      <FormControl>
                        <Input
                          leftContent={
                            <span className="text-muted-foreground">$</span>
                          }
                          placeholder="BTC"
                          type="text"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('form.description.label')}</FormLabel>
                      <FormControl>
                        <Input
                          leftContent={
                            <FeatherIcon className="text-muted-foreground" />
                          }
                          placeholder={randomPlaceholder}
                          type="text"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <Accordion
                  collapsible
                  className="-mt-4"
                  type="single"
                  value={showOptions}
                  onValueChange={setShowOptions}
                >
                  <AccordionItem value="item-1">
                    <AccordionTrigger>
                      {showOptions
                        ? t('form.hide-options')
                        : t('form.show-more-options')}
                    </AccordionTrigger>
                    <AccordionContent className="flex flex-col gap-4 px-1">
                      <FormField
                        control={form.control}
                        name="website"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>{t('form.website.label')}</FormLabel>
                            <FormControl>
                              <Input
                                leftContent={
                                  <GlobeIcon className="text-muted-foreground" />
                                }
                                placeholder="https://example.com"
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name="twitter"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>
                              {t('form.twitter.label')} ({t('form.optional')})
                            </FormLabel>
                            <FormControl>
                              <Input
                                leftContent={
                                  <XIcon className="text-muted-foreground" />
                                }
                                placeholder="https://twitter.com/yourtoken"
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name="telegram"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>
                              {t('form.telegram.label')} ({t('form.optional')})
                            </FormLabel>
                            <FormControl>
                              <Input
                                leftContent={
                                  <TelegramIcon className="text-muted-foreground" />
                                }
                                placeholder="https://t.me/yourtoken"
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </AccordionContent>
                  </AccordionItem>
                </Accordion>
                <FormField
                  control={form.control}
                  name="platform"
                  render={({ field }) => (
                    <FormItem className="-mt-4">
                      <FormLabel>{t('form.platform.label')}</FormLabel>
                      <Select
                        defaultValue={field.value}
                        onValueChange={field.onChange}
                      >
                        <FormControl>
                          <SelectTrigger className="w-full">
                            <SelectValue placeholder="Choose platform" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {PLATFORM_OPTIONS.map((option) => (
                            <SelectItem key={option} value={option}>
                              {option}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="decimals"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('form.decimals.label')}</FormLabel>
                      <FormControl>
                        <Input disabled type="number" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="tokenSupply"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('form.supply-token.label')}</FormLabel>
                      <FormControl>
                        <ToggleGroup
                          className="flex flex-wrap gap-2"
                          type="single"
                          value={field.value}
                          onValueChange={field.onChange}
                        >
                          {TOKEN_SUPPLY_OPTIONS.map((option) => (
                            <ToggleGroupItem
                              key={option.value}
                              className={cn(
                                'data-[state=on]:bg-primary data-[state=on]:text-primary-foreground',
                                'bg-card border px-4 py-2',
                              )}
                              value={option.label}
                            >
                              {option.label}
                            </ToggleGroupItem>
                          ))}
                        </ToggleGroup>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                {form.watch('tokenSupply') === 'Custom' && (
                  <FormField
                    control={form.control}
                    name="customTokenSupply"
                    render={({ field }) => (
                      <FormItem className="mb-2">
                        <FormLabel>
                          {t('form.supply-token.custom.label')}
                        </FormLabel>
                        <FormControl>
                          <FormattedNumberInput
                            placeholder="10,000,000"
                            value={field.value}
                            onChange={field.onChange}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                )}
              </div>
            </ScrollArea>

            <DialogFooter>
              {!currentAccount ? (
                <WalletConnectButton />
              ) : (
                <>
                  <Button
                    disabled={isCreating}
                    size="sm"
                    type="button"
                    variant="secondary"
                    onClick={() => props.onOpenChange?.(false)}
                  >
                    Cancel
                  </Button>
                  <Button
                    disabled={!form.formState.isValid || isCreating}
                    size="sm"
                    type="submit"
                  >
                    {isCreating ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        {creationStep === 'publishing' &&
                          'Publishing Contract...'}
                        {creationStep === 'bonding' &&
                          'Creating Bonding Curve...'}
                        {creationStep === 'complete' && 'Completing...'}
                        {creationStep === 'idle' && 'Creating...'}
                      </>
                    ) : (
                      'Create Token'
                    )}
                  </Button>
                </>
              )}
            </DialogFooter>
          </form>
        </Form>
        )}
      </DialogContent>
    </Dialog>
  );
};
